"""LLM client for the Agent Evaluation Tool."""

import os
import json
import asyncio
import httpx
import logging
import time
from typing import Dict, Any, List, Optional, Union, Tuple
import backoff
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from dotenv import load_dotenv

from src.core.config import get_config

# Load environment variables from .env file if it exists
load_dotenv()

logger = logging.getLogger(__name__)

class LLMClient:
    """Client for interacting with LLM APIs."""

    def __init__(self):
        """Initialize the LLM client."""
        self.config = get_config()
        self.default_timeout = self.config.get("llm.default_timeout", 60)
        self.max_retries = self.config.get("llm.max_retries", 3)
        self.retry_delay = self.config.get("llm.retry_delay", 2)
        self.endpoints = self._load_endpoints()
        self.client = httpx.AsyncClient(timeout=self.default_timeout)
        self.model_endpoint_mapping = self._create_model_endpoint_mapping()
        logger.info(f"Initialized LLM client with {len(self.endpoints)} endpoints")

    def _load_endpoints(self) -> Dict[str, Dict[str, Any]]:
        """Load LLM endpoints from configuration.

        Returns:
            Dictionary mapping endpoint names to endpoint configurations.
        """
        endpoints = {}
        for endpoint in self.config.get("llm.endpoints", []):
            name = endpoint.get("name", "default")
            endpoints[name] = endpoint
            logger.debug(f"Loaded endpoint: {name} -> {endpoint.get('url')}")

        if not endpoints:
            # Add a default endpoint if none are configured
            default_url = os.environ.get("LLM_API_URL", "http://localhost:8080/v1")
            default_key = os.environ.get("LLM_API_KEY", "")
            endpoints["default"] = {
                "url": default_url,
                "api_key": default_key,
                "models": ["*"]  # Wildcard to accept any model
            }
            logger.debug(f"Created default endpoint: default -> {default_url}")

        return endpoints

    def _create_model_endpoint_mapping(self) -> Dict[str, str]:
        """Create a mapping from model names to endpoint names.

        Returns:
            Dictionary mapping model names to endpoint names.
        """
        mapping = {}
        for endpoint_name, endpoint in self.endpoints.items():
            models = endpoint.get("models", [])

            # If no models are specified or "*" is in models, this endpoint accepts any model
            if not models or "*" in models:
                continue

            # Map each model to this endpoint
            for model in models:
                mapping[model] = endpoint_name
                logger.debug(f"Mapped model {model} to endpoint {endpoint_name}")

        return mapping

    def get_endpoint(self, model: str) -> Dict[str, Any]:
        """Get the endpoint configuration for a model.

        Args:
            model: Model name or endpoint name.

        Returns:
            Endpoint configuration.
        """
        # Check if we have a specific mapping for this model
        if model in self.model_endpoint_mapping:
            endpoint_name = self.model_endpoint_mapping[model]
            logger.debug(f"Using mapped endpoint {endpoint_name} for model {model}")
            return self.endpoints[endpoint_name]

        # Try to find an exact match with endpoint name
        if model in self.endpoints:
            logger.debug(f"Using endpoint {model} for model {model}")
            return self.endpoints[model]

        # Find the first endpoint that accepts any model
        for endpoint_name, endpoint in self.endpoints.items():
            models = endpoint.get("models", [])
            if not models or "*" in models:
                logger.debug(f"Using wildcard endpoint {endpoint_name} for model {model}")
                return endpoint

        # Default to the first endpoint
        default_endpoint = next(iter(self.endpoints.values()))
        logger.debug(f"Using default endpoint for model {model}")
        return default_endpoint

    def get_api_key(self, model: str) -> str:
        """Get the API key for a model.

        Args:
            model: Model name or endpoint name.

        Returns:
            API key.
        """
        endpoint = self.get_endpoint(model)
        api_key = endpoint.get("api_key", "")

        # If no API key is configured, try to get it from environment variables
        if not api_key:
            # Try model-specific environment variable
            env_var = f"LLM_API_KEY_{model.upper().replace('-', '_')}"
            api_key = os.environ.get(env_var, "")

            # Try endpoint-specific environment variable
            if not api_key and "name" in endpoint:
                env_var = f"LLM_API_KEY_{endpoint['name'].upper().replace('-', '_')}"
                api_key = os.environ.get(env_var, "")

            # Fall back to generic API key
            if not api_key:
                api_key = os.environ.get("LLM_API_KEY", "")

        return api_key

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry=retry_if_exception_type((httpx.HTTPError, asyncio.TimeoutError, ConnectionError)),
        reraise=True
    )
    async def generate(self, model: str, prompt: str, max_tokens: int = 1000,
                      temperature: float = 0.7, stop: Optional[List[str]] = None,
                      retry_on_error: bool = True) -> str:
        """Generate text using an LLM.

        Args:
            model: Model name or endpoint name.
            prompt: Input prompt.
            max_tokens: Maximum number of tokens to generate.
            temperature: Sampling temperature.
            stop: Stop sequences.
            retry_on_error: Whether to retry on error.

        Returns:
            Generated text.

        Raises:
            httpx.HTTPStatusError: If the HTTP request fails.
            asyncio.TimeoutError: If the request times out.
            ValueError: If the response is invalid.
            Exception: For other errors.
        """
        endpoint = self.get_endpoint(model)
        url = f"{endpoint['url']}/chat/completions"

        headers = {
            "Content-Type": "application/json"
        }

        api_key = self.get_api_key(model)
        if api_key:
            headers["Authorization"] = f"Bearer {api_key}"

        data = {
            "model": model,
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": max_tokens,
            "temperature": temperature
        }

        if stop:
            data["stop"] = stop

        # Add any endpoint-specific parameters
        for key, value in endpoint.get("parameters", {}).items():
            if key not in data:
                data[key] = value

        start_time = time.time()
        try:
            logger.debug(f"Sending request to {url} for model {model}")
            response = await self.client.post(url, headers=headers, json=data)
            response.raise_for_status()

            result = response.json()

            if "choices" not in result or not result["choices"]:
                raise ValueError(f"Invalid response from LLM API: {result}")

            content = result["choices"][0]["message"]["content"]
            elapsed = time.time() - start_time
            logger.debug(f"LLM generation completed in {elapsed:.2f}s")

            return content

        except httpx.HTTPStatusError as e:
            elapsed = time.time() - start_time
            status_code = e.response.status_code

            # Don't retry on client errors (4xx) except for rate limiting (429)
            if status_code < 500 and status_code != 429 and retry_on_error:
                logger.error(f"HTTP client error during LLM generation: {status_code} {e.response.text}")
                raise

            logger.warning(f"HTTP error during LLM generation (will retry): {status_code} {e.response.text}")
            raise

        except asyncio.TimeoutError:
            elapsed = time.time() - start_time
            logger.warning(f"Timeout during LLM generation after {elapsed:.2f}s (will retry)")
            raise

        except Exception as e:
            elapsed = time.time() - start_time
            logger.error(f"Error during LLM generation after {elapsed:.2f}s: {e}")
            raise

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry=retry_if_exception_type((httpx.HTTPError, asyncio.TimeoutError, ConnectionError)),
        reraise=True
    )
    async def chat(self, model: str, messages: List[Dict[str, str]], max_tokens: int = 1000,
                  temperature: float = 0.7, stop: Optional[List[str]] = None,
                  retry_on_error: bool = True) -> Dict[str, Any]:
        """Generate a chat response using an LLM.

        Args:
            model: Model name or endpoint name.
            messages: Chat messages.
            max_tokens: Maximum number of tokens to generate.
            temperature: Sampling temperature.
            stop: Stop sequences.
            retry_on_error: Whether to retry on error.

        Returns:
            Chat response.

        Raises:
            httpx.HTTPStatusError: If the HTTP request fails.
            asyncio.TimeoutError: If the request times out.
            ValueError: If the response is invalid.
            Exception: For other errors.
        """
        endpoint = self.get_endpoint(model)
        url = f"{endpoint['url']}/chat/completions"

        headers = {
            "Content-Type": "application/json"
        }

        api_key = self.get_api_key(model)
        if api_key:
            headers["Authorization"] = f"Bearer {api_key}"

        data = {
            "model": model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature
        }

        if stop:
            data["stop"] = stop

        # Add any endpoint-specific parameters
        for key, value in endpoint.get("parameters", {}).items():
            if key not in data:
                data[key] = value

        start_time = time.time()
        try:
            logger.debug(f"Sending chat request to {url} for model {model}")
            response = await self.client.post(url, headers=headers, json=data)
            response.raise_for_status()

            result = response.json()
            elapsed = time.time() - start_time
            logger.debug(f"LLM chat completed in {elapsed:.2f}s")

            return result

        except httpx.HTTPStatusError as e:
            elapsed = time.time() - start_time
            status_code = e.response.status_code

            # Don't retry on client errors (4xx) except for rate limiting (429)
            if status_code < 500 and status_code != 429 and retry_on_error:
                logger.error(f"HTTP client error during LLM chat: {status_code} {e.response.text}")
                raise

            logger.warning(f"HTTP error during LLM chat (will retry): {status_code} {e.response.text}")
            raise

        except asyncio.TimeoutError:
            elapsed = time.time() - start_time
            logger.warning(f"Timeout during LLM chat after {elapsed:.2f}s (will retry)")
            raise

        except Exception as e:
            elapsed = time.time() - start_time
            logger.error(f"Error during LLM chat after {elapsed:.2f}s: {e}")
            raise

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry=retry_if_exception_type((httpx.HTTPError, asyncio.TimeoutError, ConnectionError)),
        reraise=True
    )
    async def function_calling(self, model: str, messages: List[Dict[str, str]],
                              functions: List[Dict[str, Any]], max_tokens: int = 1000,
                              temperature: float = 0.7, retry_on_error: bool = True) -> Dict[str, Any]:
        """Generate a function call using an LLM.

        Args:
            model: Model name or endpoint name.
            messages: Chat messages.
            functions: Function definitions.
            max_tokens: Maximum number of tokens to generate.
            temperature: Sampling temperature.
            retry_on_error: Whether to retry on error.

        Returns:
            Function call response.

        Raises:
            httpx.HTTPStatusError: If the HTTP request fails.
            asyncio.TimeoutError: If the request times out.
            ValueError: If the response is invalid.
            Exception: For other errors.
        """
        endpoint = self.get_endpoint(model)
        url = f"{endpoint['url']}/chat/completions"

        headers = {
            "Content-Type": "application/json"
        }

        api_key = self.get_api_key(model)
        if api_key:
            headers["Authorization"] = f"Bearer {api_key}"

        data = {
            "model": model,
            "messages": messages,
            "functions": functions,
            "max_tokens": max_tokens,
            "temperature": temperature
        }

        # Add any endpoint-specific parameters
        for key, value in endpoint.get("parameters", {}).items():
            if key not in data:
                data[key] = value

        start_time = time.time()
        try:
            logger.debug(f"Sending function call request to {url} for model {model}")
            response = await self.client.post(url, headers=headers, json=data)
            response.raise_for_status()

            result = response.json()
            elapsed = time.time() - start_time
            logger.debug(f"LLM function call completed in {elapsed:.2f}s")

            return result

        except httpx.HTTPStatusError as e:
            elapsed = time.time() - start_time
            status_code = e.response.status_code

            # Don't retry on client errors (4xx) except for rate limiting (429)
            if status_code < 500 and status_code != 429 and retry_on_error:
                logger.error(f"HTTP client error during LLM function call: {status_code} {e.response.text}")
                raise

            logger.warning(f"HTTP error during LLM function call (will retry): {status_code} {e.response.text}")
            raise

        except asyncio.TimeoutError:
            elapsed = time.time() - start_time
            logger.warning(f"Timeout during LLM function call after {elapsed:.2f}s (will retry)")
            raise

        except Exception as e:
            elapsed = time.time() - start_time
            logger.error(f"Error during LLM function call after {elapsed:.2f}s: {e}")
            raise

    async def close(self):
        """Close the client."""
        await self.client.aclose()
        logger.debug("LLM client closed")
