"""AutoGen wrapper for the Agent Evaluation Tool."""

import asyncio
import json
import logging
import time
import os
from typing import Dict, Any, List, Optional, Tuple, Callable
from concurrent.futures import ThreadPoolExecutor

# Import AutoGen
try:
    import autogen
    from autogen import Agent, AssistantAgent, UserProxyAgent, config_list_from_json
    AUTOGEN_AVAILABLE = True
except ImportError:
    AUTOGEN_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning("AutoGen is not available. Install it with 'pip install autogen-agentchat'")

from src.llm.client import LLMClient
from src.core.config import get_config

logger = logging.getLogger(__name__)

class AutoGenWrapper:
    """Wrapper for AutoGen framework."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the AutoGen wrapper.

        Args:
            config: Wrapper configuration.
        """
        self.config = config
        self.llm_client = LLMClient()
        self.executor = ThreadPoolExecutor(max_workers=5)

        # Check if AutoGen is available
        if not AUTOGEN_AVAILABLE:
            logger.warning("AutoGen is not available. Some functionality may be limited.")

    def _create_autogen_config(self, model: str) -> List[Dict[str, Any]]:
        """Create AutoGen configuration for a model.

        Args:
            model: Model name.

        Returns:
            AutoGen configuration list.
        """
        endpoint = self.llm_client.get_endpoint(model)
        api_key = self.llm_client.get_api_key(model)

        # Create a configuration for the model
        config = {
            "model": model,
            "api_key": api_key,
            "base_url": endpoint.get("url", "http://localhost:8080/v1"),
        }

        # Add any additional parameters from the endpoint configuration
        for key, value in endpoint.get("parameters", {}).items():
            if key not in config:
                config[key] = value

        return [config]

    def _run_autogen_sync(self, task_description: str, model: str,
                         max_steps: int, timeout: int) -> Tuple[List[Dict[str, Any]], str]:
        """Run AutoGen synchronously.

        Args:
            task_description: Task description.
            model: Model name.
            max_steps: Maximum number of steps.
            timeout: Timeout in seconds.

        Returns:
            Tuple of (steps, final_answer).
        """
        if not AUTOGEN_AVAILABLE:
            raise ImportError("AutoGen is not available. Install it with 'pip install autogen-agentchat'")

        # Create AutoGen configuration
        config_list = self._create_autogen_config(model)

        # Create agents
        assistant = AssistantAgent(
            name="assistant",
            llm_config={"config_list": config_list},
            system_message=self.config.get("assistant_system_message",
                "You are a helpful AI assistant that solves tasks step by step.")
        )

        # Create a termination message function
        def is_termination_msg(message: Dict[str, Any]) -> bool:
            """Check if a message is a termination message."""
            if isinstance(message, dict) and "content" in message:
                content = message["content"].lower()
                return "task completed" in content or "final answer" in content
            return False

        # Create a user proxy agent
        user_proxy = UserProxyAgent(
            name="user_proxy",
            human_input_mode="NEVER",
            max_consecutive_auto_reply=max_steps,
            is_termination_msg=is_termination_msg,
            code_execution_config={"use_docker": False} if self.config.get("allow_code_execution", False) else None
        )

        # Start the conversation
        user_proxy.initiate_chat(
            assistant,
            message=task_description,
            max_turns=max_steps
        )

        # Get the conversation history
        conversation = user_proxy.chat_history[assistant]

        # Process the conversation into steps
        steps = []
        for i, message in enumerate(conversation):
            if message["role"] == "assistant":
                steps.append({
                    "step": i + 1,
                    "role": "assistant",
                    "content": message["content"]
                })
            elif message["role"] == "user" and i > 0:  # Skip the initial user message
                steps.append({
                    "step": i + 1,
                    "role": "user",
                    "content": message["content"]
                })

        # Extract the final answer (last assistant message)
        final_answer = ""
        for message in reversed(conversation):
            if message["role"] == "assistant":
                final_answer = message["content"]
                break

        return steps, final_answer

    async def execute_task(self, task_definition: Dict[str, Any], model: str,
                          max_steps: int = 10, timeout: int = 600) -> Dict[str, Any]:
        """Execute a task using AutoGen.

        Args:
            task_definition: Task definition.
            model: Model to use.
            max_steps: Maximum number of steps.
            timeout: Timeout in seconds.

        Returns:
            Task execution results.
        """
        try:
            task_name = task_definition.get("name", "unknown")
            task_description = task_definition.get("description", "")
            task_input = task_definition.get("input", {})

            # Format the task description with input
            formatted_description = task_description
            if task_input:
                formatted_description += "\n\nInput:\n"
                if isinstance(task_input, dict):
                    for key, value in task_input.items():
                        formatted_description += f"{key}: {value}\n"
                elif isinstance(task_input, str):
                    formatted_description += task_input

            logger.info(f"Executing task using AutoGen: {task_name}")

            start_time = time.time()

            if AUTOGEN_AVAILABLE:
                # Run AutoGen in a separate thread to avoid blocking the event loop
                loop = asyncio.get_event_loop()
                steps, final_answer = await loop.run_in_executor(
                    self.executor,
                    self._run_autogen_sync,
                    formatted_description,
                    model,
                    max_steps,
                    timeout
                )
            else:
                # Fallback to using the LLM client directly if AutoGen is not available
                logger.warning("AutoGen not available, falling back to direct LLM calls")
                steps = []
                for i in range(min(3, max_steps)):
                    thinking = await self.llm_client.generate(
                        model=model,
                        prompt=f"Task: {formatted_description}\nStep {i+1}: Think about how to solve this task.",
                        max_tokens=200
                    )

                    action = await self.llm_client.generate(
                        model=model,
                        prompt=f"Task: {formatted_description}\nThinking: {thinking}\nStep {i+1}: What action to take?",
                        max_tokens=100
                    )

                    steps.append({
                        "step": i+1,
                        "role": "assistant",
                        "content": f"Thinking: {thinking}\n\nAction: {action}"
                    })

                final_answer = await self.llm_client.generate(
                    model=model,
                    prompt=f"Task: {formatted_description}\nProvide the final answer to this task.",
                    max_tokens=300
                )

            # Calculate execution time
            execution_time = time.time() - start_time

            # Evaluate the answer if evaluation criteria are provided
            score = None
            if "evaluation" in task_definition:
                score = await self._evaluate_answer(
                    task_definition, final_answer, model
                )

            # If no score was calculated, use a placeholder
            if score is None:
                score = 0.75  # Placeholder score

            return {
                "task_name": task_name,
                "model": model,
                "framework": "autogen",
                "steps": steps,
                "final_answer": final_answer,
                "score": score,
                "metrics": {
                    "steps_taken": len(steps),
                    "time_taken": execution_time
                }
            }

        except Exception as e:
            logger.error(f"Error executing task with AutoGen: {e}", exc_info=True)
            raise

    async def _evaluate_answer(self, task_definition: Dict[str, Any],
                              answer: str, model: str) -> float:
        """Evaluate an answer against the task's evaluation criteria.

        Args:
            task_definition: Task definition.
            answer: Answer to evaluate.
            model: Model to use for evaluation.

        Returns:
            Evaluation score (0.0 to 1.0).
        """
        evaluation = task_definition.get("evaluation", {})
        expected_output = evaluation.get("expected_output")

        if not expected_output:
            return None

        # Simple exact match evaluation
        if isinstance(expected_output, str) and expected_output.strip() == answer.strip():
            return 1.0

        # Use the LLM to evaluate the answer
        prompt = f"""
        Task: {task_definition.get('description', '')}

        Expected Output: {expected_output}

        Actual Output: {answer}

        Evaluate the actual output against the expected output.
        Consider correctness, completeness, and relevance.

        Score the answer on a scale from 0.0 to 1.0, where:
        - 0.0 means completely incorrect or irrelevant
        - 0.5 means partially correct
        - 1.0 means completely correct

        Return only the numeric score.
        """

        try:
            score_text = await self.llm_client.generate(
                model=model,
                prompt=prompt,
                max_tokens=10
            )

            # Extract the numeric score
            score_text = score_text.strip()
            for word in score_text.split():
                try:
                    score = float(word)
                    if 0.0 <= score <= 1.0:
                        return score
                except ValueError:
                    continue

            # If no valid score was found, return a default
            return 0.5

        except Exception as e:
            logger.error(f"Error evaluating answer: {e}")
            return 0.5
