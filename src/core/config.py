"""Configuration management for the Agent Evaluation Tool."""

import os
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional

from src.core.logging_config import setup_logging

logger = logging.getLogger(__name__)

class Config:
    """Configuration manager for the Agent Evaluation Tool."""

    def __init__(self, config_path: Optional[str] = None):
        """Initialize the configuration manager.

        Args:
            config_path: Path to the custom configuration file. If None, only the default config is loaded.
        """
        self.config_dir = Path(os.environ.get("CONFIG_DIR", "config"))
        self.default_config_path = self.config_dir / "default.yaml"
        self.config = self._load_default_config()

        if config_path:
            custom_config = self._load_config(config_path)
            self._merge_configs(custom_config)

        # Override with environment variables
        self._apply_env_overrides()

        # Configure logging based on config
        self._configure_logging()

    def _load_default_config(self) -> Dict[str, Any]:
        """Load the default configuration."""
        try:
            return self._load_config(self.default_config_path)
        except Exception as e:
            logger.error(f"Failed to load default config: {e}")
            return {}

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load a configuration file.

        Args:
            config_path: Path to the configuration file.

        Returns:
            The loaded configuration as a dictionary.
        """
        path = Path(config_path)
        if not path.exists():
            logger.warning(f"Config file not found: {path}")
            return {}

        try:
            with open(path, "r") as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Error loading config from {path}: {e}")
            return {}

    def _merge_configs(self, custom_config: Dict[str, Any]) -> None:
        """Merge custom configuration into the default configuration.

        Args:
            custom_config: Custom configuration to merge.
        """
        def _merge_dicts(base: Dict[str, Any], override: Dict[str, Any]) -> Dict[str, Any]:
            """Recursively merge two dictionaries."""
            for key, value in override.items():
                if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                    _merge_dicts(base[key], value)
                else:
                    base[key] = value
            return base

        self.config = _merge_dicts(self.config, custom_config)

    def _apply_env_overrides(self) -> None:
        """Apply environment variable overrides to the configuration."""
        # LLM API keys
        for i, endpoint in enumerate(self.config.get("llm", {}).get("endpoints", [])):
            env_var = f"LLM_API_KEY_{endpoint['name'].upper()}" if endpoint.get('name') else f"LLM_API_KEY_{i+1}"
            if os.environ.get(env_var):
                endpoint["api_key"] = os.environ.get(env_var)
            elif os.environ.get("LLM_API_KEY"):  # Fallback to generic API key
                endpoint["api_key"] = os.environ.get("LLM_API_KEY")

        # Database password
        if "postgres" in self.config.get("storage", {}):
            if os.environ.get("DB_PASSWORD"):
                self.config["storage"]["postgres"]["password"] = os.environ.get("DB_PASSWORD")

    def _configure_logging(self) -> None:
        """Configure logging based on the configuration."""
        logging_config = self.config.get("logging", {})
        setup_logging(logging_config)

    def get(self, key: str, default: Any = None) -> Any:
        """Get a configuration value.

        Args:
            key: Dot-separated path to the configuration value.
            default: Default value to return if the key is not found.

        Returns:
            The configuration value or the default value if not found.
        """
        parts = key.split(".")
        value = self.config

        for part in parts:
            if isinstance(value, dict) and part in value:
                value = value[part]
            else:
                return default

        return value

    def get_all(self) -> Dict[str, Any]:
        """Get the entire configuration.

        Returns:
            The entire configuration as a dictionary.
        """
        return self.config


# Global configuration instance
_config_instance = None

def get_config(config_path: Optional[str] = None) -> Config:
    """Get the global configuration instance.

    Args:
        config_path: Path to the custom configuration file.

    Returns:
        The global configuration instance.
    """
    global _config_instance
    if _config_instance is None:
        _config_instance = Config(config_path)
    return _config_instance
