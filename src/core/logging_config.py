"""Logging configuration for the Agent Evaluation Tool."""

import os
import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any, Optional


def setup_logging(config: Optional[Dict[str, Any]] = None) -> None:
    """Set up logging configuration.
    
    Args:
        config: Logging configuration dictionary. If None, uses default settings.
    """
    if config is None:
        config = {}
    
    # Get logging configuration
    log_level = config.get("level", "INFO").upper()
    log_file = config.get("file", "./logs/agent_bench.log")
    max_size_mb = config.get("max_size_mb", 10)
    backup_count = config.get("backup_count", 5)
    
    # Create logs directory if it doesn't exist
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level, logging.INFO))
    
    # Clear existing handlers
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create formatter
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, log_level, logging.INFO))
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=max_size_mb * 1024 * 1024,  # Convert MB to bytes
        backupCount=backup_count
    )
    file_handler.setLevel(getattr(logging, log_level, logging.INFO))
    file_handler.setFormatter(formatter)
    root_logger.addHandler(file_handler)
    
    # Log the configuration
    logger = logging.getLogger(__name__)
    logger.info(f"Logging configured: level={log_level}, file={log_file}, "
               f"max_size={max_size_mb}MB, backups={backup_count}")


def get_logger(name: str) -> logging.Logger:
    """Get a logger with the specified name.
    
    Args:
        name: Logger name.
        
    Returns:
        Logger instance.
    """
    return logging.getLogger(name)
