"""API routes for the Agent Evaluation Tool."""

from fastapi import APIRouter, HTTPException, Depends, Query, Path, BackgroundTasks
from typing import Dict, Any, List, Optional, Union
import asyncio

from src.api.models import (
    TaskRequest, BatchTaskRequest, TaskResponse, TaskResult, BenchmarkInfo,
    TaskInfo, ResultSummary, ErrorResponse, TaskStatus, WorkerStatsResponse
)
from src.core.queue import get_task_queue
from src.core.worker import get_worker_stats
from src.storage.db import get_storage
from src.adapters.base import BaseAdapter

router = APIRouter()


@router.post("/tasks", response_model=TaskResponse, tags=["tasks"])
async def create_task(request: TaskRequest):
    """Create a new evaluation task.

    Args:
        request: Task request.

    Returns:
        Task response.
    """
    task_queue = get_task_queue()

    # Prepare parameters
    params = request.params or {}
    params["model"] = request.model

    if request.task:
        params["task"] = request.task

    if request.framework:
        params["framework"] = request.framework

    # Enqueue the task
    task_id = await task_queue.enqueue(request.benchmark, params)

    # Get the task
    task = await task_queue.get_task(task_id)

    return TaskResponse(
        task_id=task.task_id,
        benchmark=task.task_type,
        model=task.params.get("model"),
        task=task.params.get("task"),
        status=task.status,
        created_at=task.created_at,
        started_at=task.started_at,
        completed_at=task.completed_at
    )


@router.post("/tasks/batch", response_model=List[TaskResponse], tags=["tasks"])
async def create_batch_tasks(request: BatchTaskRequest, background_tasks: BackgroundTasks):
    """Create multiple evaluation tasks for different models.

    Args:
        request: Batch task request.
        background_tasks: Background tasks.

    Returns:
        List of task responses.
    """
    task_queue = get_task_queue()
    responses = []

    # Process each model in the batch
    for model in request.models:
        # Prepare parameters
        params = request.params.copy() if request.params else {}
        params["model"] = model

        if request.framework:
            params["framework"] = request.framework

        # If specific tasks are requested, create a task for each
        if request.tasks:
            for task_name in request.tasks:
                task_params = params.copy()
                task_params["task"] = task_name

                # Enqueue the task
                task_id = await task_queue.enqueue(request.benchmark, task_params)

                # Get the task
                task = await task_queue.get_task(task_id)

                responses.append(TaskResponse(
                    task_id=task.task_id,
                    benchmark=task.task_type,
                    model=task.params.get("model"),
                    task=task.params.get("task"),
                    status=task.status,
                    created_at=task.created_at,
                    started_at=task.started_at,
                    completed_at=task.completed_at
                ))
        else:
            # No specific tasks, create a single task for all tasks
            # Enqueue the task
            task_id = await task_queue.enqueue(request.benchmark, params)

            # Get the task
            task = await task_queue.get_task(task_id)

            responses.append(TaskResponse(
                task_id=task.task_id,
                benchmark=task.task_type,
                model=task.params.get("model"),
                task=task.params.get("task"),
                status=task.status,
                created_at=task.created_at,
                started_at=task.started_at,
                completed_at=task.completed_at
            ))

    return responses


@router.get("/tasks", response_model=List[TaskResponse], tags=["tasks"])
async def list_tasks(status: Optional[TaskStatus] = None):
    """List all tasks.

    Args:
        status: Filter tasks by status.

    Returns:
        List of tasks.
    """
    task_queue = get_task_queue()
    tasks = await task_queue.list_tasks(status)

    return [
        TaskResponse(
            task_id=task["task_id"],
            benchmark=task["task_type"],
            model=task["params"].get("model"),
            task=task["params"].get("task"),
            status=task["status"],
            created_at=task["created_at"],
            started_at=task["started_at"],
            completed_at=task["completed_at"]
        )
        for task in tasks
    ]


@router.get("/tasks/{task_id}", response_model=TaskResult, tags=["tasks"])
async def get_task(task_id: str):
    """Get a task by ID.

    Args:
        task_id: Task ID.

    Returns:
        Task result.
    """
    task_queue = get_task_queue()
    task = await task_queue.get_task(task_id)

    if not task:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

    return TaskResult(
        task_id=task.task_id,
        benchmark=task.task_type,
        model=task.params.get("model"),
        task=task.params.get("task"),
        status=task.status,
        result=task.result,
        error=task.error,
        created_at=task.created_at,
        started_at=task.started_at,
        completed_at=task.completed_at
    )


@router.delete("/tasks/{task_id}", response_model=Dict[str, Any], tags=["tasks"])
async def cancel_task(task_id: str):
    """Cancel a task.

    Args:
        task_id: Task ID.

    Returns:
        Cancellation result.
    """
    task_queue = get_task_queue()
    success = await task_queue.cancel_task(task_id)

    if not success:
        raise HTTPException(status_code=400, detail=f"Failed to cancel task {task_id}")

    return {"success": True, "message": f"Task {task_id} cancelled"}


@router.get("/benchmarks", response_model=List[BenchmarkInfo], tags=["benchmarks"])
async def list_benchmarks(adapters: Dict[str, BaseAdapter] = Depends(lambda: {})):
    """List all benchmarks.

    Args:
        adapters: Benchmark adapters.

    Returns:
        List of benchmarks.
    """
    benchmarks = []

    for name, adapter in adapters.items():
        tasks = await adapter.list_tasks()

        benchmarks.append(
            BenchmarkInfo(
                name=name,
                description=f"{name} benchmark",
                tasks=tasks
            )
        )

    return benchmarks


@router.get("/benchmarks/{benchmark}/tasks", response_model=List[TaskInfo], tags=["benchmarks"])
async def list_benchmark_tasks(benchmark: str, adapters: Dict[str, BaseAdapter] = Depends(lambda: {})):
    """List tasks for a benchmark.

    Args:
        benchmark: Benchmark name.
        adapters: Benchmark adapters.

    Returns:
        List of tasks.
    """
    if benchmark not in adapters:
        raise HTTPException(status_code=404, detail=f"Benchmark {benchmark} not found")

    adapter = adapters[benchmark]
    tasks = await adapter.list_tasks()

    return [
        TaskInfo(
            id=task["id"],
            name=task["name"],
            description=task["description"],
            parameters=task.get("parameters"),
            metrics=task.get("metrics")
        )
        for task in tasks
    ]


@router.get("/benchmarks/{benchmark}/tasks/{task_id}", response_model=TaskInfo, tags=["benchmarks"])
async def get_benchmark_task(benchmark: str, task_id: str, adapters: Dict[str, BaseAdapter] = Depends(lambda: {})):
    """Get a task by ID.

    Args:
        benchmark: Benchmark name.
        task_id: Task ID.
        adapters: Benchmark adapters.

    Returns:
        Task information.
    """
    if benchmark not in adapters:
        raise HTTPException(status_code=404, detail=f"Benchmark {benchmark} not found")

    adapter = adapters[benchmark]
    task = await adapter.get_task_details(task_id)

    if not task:
        raise HTTPException(status_code=404, detail=f"Task {task_id} not found")

    return TaskInfo(
        id=task["id"],
        name=task["name"],
        description=task["description"],
        parameters=task.get("parameters"),
        metrics=task.get("metrics")
    )


@router.get("/results", response_model=List[ResultSummary], tags=["results"])
async def list_results(benchmark: Optional[str] = None, model: Optional[str] = None):
    """List evaluation results.

    Args:
        benchmark: Filter by benchmark.
        model: Filter by model.

    Returns:
        List of result summaries.
    """
    storage = get_storage()
    results = await storage.list_results(benchmark, model)

    return [
        ResultSummary(
            id=result["id"],
            benchmark=result["benchmark"],
            model=result["model"],
            task=result["task"],
            framework=result.get("framework"),
            created_at=result["created_at"]
        )
        for result in results
    ]


@router.get("/results/{result_id}", response_model=Dict[str, Any], tags=["results"])
async def get_result(result_id: str):
    """Get an evaluation result.

    Args:
        result_id: Result ID.

    Returns:
        Evaluation result.
    """
    storage = get_storage()
    result = await storage.get_result(result_id)

    if not result:
        raise HTTPException(status_code=404, detail=f"Result {result_id} not found")

    return result


@router.get("/stats", response_model=WorkerStatsResponse, tags=["system"])
async def get_stats():
    """Get worker pool statistics.

    Returns:
        Worker pool statistics.
    """
    from src.core.worker import get_worker_pool

    worker_pool = get_worker_pool()
    if worker_pool:
        stats = await worker_pool.get_stats()
        return WorkerStatsResponse(
            active_workers=len(worker_pool.workers),
            tasks_completed=stats.get("tasks_completed", 0),
            tasks_failed=stats.get("tasks_failed", 0),
            tasks_cancelled=stats.get("tasks_cancelled", 0),
            total_execution_time=stats.get("total_execution_time", 0.0),
            model_stats=stats.get("model_stats", {})
        )
    else:
        return WorkerStatsResponse(
            active_workers=0,
            tasks_completed=0,
            tasks_failed=0,
            tasks_cancelled=0,
            total_execution_time=0.0,
            model_stats={}
        )


@router.delete("/results/{result_id}", response_model=Dict[str, Any], tags=["results"])
async def delete_result(result_id: str):
    """Delete an evaluation result.

    Args:
        result_id: Result ID.

    Returns:
        Deletion result.
    """
    storage = get_storage()
    success = await storage.delete_result(result_id)

    if not success:
        raise HTTPException(status_code=404, detail=f"Result {result_id} not found")

    return {"success": True, "message": f"Result {result_id} deleted"}
