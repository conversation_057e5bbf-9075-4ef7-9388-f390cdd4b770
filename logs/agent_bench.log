2025-05-27 10:54:48,558 - src.core.logging_config - INFO - Logging configured: level=INFO, file=./logs/agent_bench.log, max_size=10MB, backups=5
2025-05-27 10:54:48,558 - test_logger - INFO - This is a test info message
2025-05-27 10:54:48,558 - test_logger - WARNING - This is a test warning message
2025-05-27 10:54:48,558 - test_logger - ERROR - This is a test error message
2025-05-27 10:54:48,570 - src.storage.db - INFO - Initialized SQLite database at data/results.db
2025-05-27 10:54:48,573 - src.storage.db - INFO - Saved result test_result_001 to SQLite database
2025-05-28 17:27:05,864 - src.core.logging_config - INFO - Logging configured: level=INFO, file=./logs/agent_bench.log, max_size=10MB, backups=5
2025-05-28 17:27:05,901 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:27:05,915 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:27:05,949 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:27:06,040 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:27:06,066 - httpx - INFO - HTTP Request: POST http://test/api/tasks/batch "HTTP/1.1 200 OK"
2025-05-28 17:27:06,085 - httpx - INFO - HTTP Request: GET http://test/api/tasks/test-task-123 "HTTP/1.1 200 OK"
2025-05-28 17:27:06,094 - httpx - INFO - HTTP Request: GET http://test/api/tasks/non-existent "HTTP/1.1 404 Not Found"
2025-05-28 17:27:06,117 - httpx - INFO - HTTP Request: GET http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:27:06,126 - src.core.queue - INFO - Task 2e27f6f4-1c7a-446d-93a4-217085ba8411 (tau_bench) enqueued
2025-05-28 17:27:06,126 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:27:06,127 - httpx - INFO - HTTP Request: GET http://test/api/tasks/2e27f6f4-1c7a-446d-93a4-217085ba8411 "HTTP/1.1 200 OK"
2025-05-28 17:27:06,127 - src.core.queue - INFO - Task 2e27f6f4-1c7a-446d-93a4-217085ba8411 status updated to running
2025-05-28 17:27:06,127 - httpx - INFO - HTTP Request: GET http://test/api/tasks/2e27f6f4-1c7a-446d-93a4-217085ba8411 "HTTP/1.1 200 OK"
2025-05-28 17:27:06,127 - src.core.queue - INFO - Task 2e27f6f4-1c7a-446d-93a4-217085ba8411 status updated to completed
2025-05-28 17:27:06,127 - httpx - INFO - HTTP Request: GET http://test/api/tasks/2e27f6f4-1c7a-446d-93a4-217085ba8411 "HTTP/1.1 200 OK"
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task 9e7aa9ff-44a0-4fc2-aff9-ec9d92e5658c (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task 2e57b540-091b-4cb2-a5a7-0697877b2caf (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task c8831273-b6a4-4b14-b7c0-91a2b55c9080 (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task 0eb9f804-ffbd-472a-8957-24edf6cf04f3 (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task 06df05aa-b894-4df3-8d12-1cf2a9dd6635 (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task 0e89a473-0c3a-4830-a940-56f845bb19a6 (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task 91df8677-923b-4146-8bb4-e1e11b5c80b8 (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task cb44d01d-80db-4d56-8819-4a9b7712b0b3 (test_benchmark) enqueued
2025-05-28 17:27:06,128 - src.core.queue - INFO - Task f814f514-dd17-466c-b4eb-39bfd80b4092 (test_benchmark) enqueued
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task b3ccf0e1-4ea4-4709-b0be-47cd69d98cd7 (test_benchmark) enqueued
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task 9e7aa9ff-44a0-4fc2-aff9-ec9d92e5658c status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task 2e57b540-091b-4cb2-a5a7-0697877b2caf status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task c8831273-b6a4-4b14-b7c0-91a2b55c9080 status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task 0eb9f804-ffbd-472a-8957-24edf6cf04f3 status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task 06df05aa-b894-4df3-8d12-1cf2a9dd6635 status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task 0e89a473-0c3a-4830-a940-56f845bb19a6 status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task 91df8677-923b-4146-8bb4-e1e11b5c80b8 status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task cb44d01d-80db-4d56-8819-4a9b7712b0b3 status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task f814f514-dd17-466c-b4eb-39bfd80b4092 status updated to running
2025-05-28 17:27:06,129 - src.core.queue - INFO - Task b3ccf0e1-4ea4-4709-b0be-47cd69d98cd7 status updated to running
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task 9e7aa9ff-44a0-4fc2-aff9-ec9d92e5658c status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task 2e57b540-091b-4cb2-a5a7-0697877b2caf status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task c8831273-b6a4-4b14-b7c0-91a2b55c9080 status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task 0eb9f804-ffbd-472a-8957-24edf6cf04f3 status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task 06df05aa-b894-4df3-8d12-1cf2a9dd6635 status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task 0e89a473-0c3a-4830-a940-56f845bb19a6 status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task 91df8677-923b-4146-8bb4-e1e11b5c80b8 status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task cb44d01d-80db-4d56-8819-4a9b7712b0b3 status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task f814f514-dd17-466c-b4eb-39bfd80b4092 status updated to completed
2025-05-28 17:27:06,140 - src.core.queue - INFO - Task b3ccf0e1-4ea4-4709-b0be-47cd69d98cd7 status updated to completed
2025-05-28 17:27:56,019 - src.core.logging_config - INFO - Logging configured: level=INFO, file=./logs/agent_bench.log, max_size=10MB, backups=5
2025-05-28 17:27:56,049 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:27:56,063 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:27:56,077 - src.llm.client - INFO - Initialized LLM client with 4 endpoints
2025-05-28 17:27:56,087 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:27:56,104 - httpx - INFO - HTTP Request: POST http://test/api/tasks/batch "HTTP/1.1 200 OK"
2025-05-28 17:27:56,109 - httpx - INFO - HTTP Request: GET http://test/api/tasks/test-task-123 "HTTP/1.1 200 OK"
2025-05-28 17:27:56,115 - httpx - INFO - HTTP Request: GET http://test/api/tasks/non-existent "HTTP/1.1 404 Not Found"
2025-05-28 17:27:56,121 - httpx - INFO - HTTP Request: GET http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:27:56,125 - src.core.queue - INFO - Task 61266005-d1ec-46e0-a9ee-88692cbaf2b6 (tau_bench) enqueued
2025-05-28 17:27:56,126 - httpx - INFO - HTTP Request: POST http://test/api/tasks "HTTP/1.1 200 OK"
2025-05-28 17:27:56,126 - httpx - INFO - HTTP Request: GET http://test/api/tasks/61266005-d1ec-46e0-a9ee-88692cbaf2b6 "HTTP/1.1 200 OK"
2025-05-28 17:27:56,126 - src.core.queue - INFO - Task 61266005-d1ec-46e0-a9ee-88692cbaf2b6 status updated to running
2025-05-28 17:27:56,126 - httpx - INFO - HTTP Request: GET http://test/api/tasks/61266005-d1ec-46e0-a9ee-88692cbaf2b6 "HTTP/1.1 200 OK"
2025-05-28 17:27:56,126 - src.core.queue - INFO - Task 61266005-d1ec-46e0-a9ee-88692cbaf2b6 status updated to completed
2025-05-28 17:27:56,126 - httpx - INFO - HTTP Request: GET http://test/api/tasks/61266005-d1ec-46e0-a9ee-88692cbaf2b6 "HTTP/1.1 200 OK"
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task aebc5214-307a-4651-a832-685ad90cde8b (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task 91efe4b9-b9db-43e4-9fa8-ed981f0002c1 (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task 8e67677c-fb50-4c54-b90c-21f7037bd9e0 (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task 2029c0bf-b6c7-433b-96c9-c072653ce044 (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task 9e9da07b-86b5-4dc3-8a55-08b0a23edaef (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task 71af4f14-0458-4f1e-9eb8-6530d1f407d1 (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task a5e395b7-847d-4add-8605-a62f9f1a8d76 (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task b769a8bc-dd12-44f6-8c30-0a3ed869877f (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task fd6f0630-5d4f-459a-86d0-32587119941c (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task 33f5c522-24ae-40dc-b0fe-5948cfc629a5 (test_benchmark) enqueued
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task aebc5214-307a-4651-a832-685ad90cde8b status updated to running
2025-05-28 17:27:56,127 - src.core.queue - INFO - Task 91efe4b9-b9db-43e4-9fa8-ed981f0002c1 status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task 8e67677c-fb50-4c54-b90c-21f7037bd9e0 status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task 2029c0bf-b6c7-433b-96c9-c072653ce044 status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task 9e9da07b-86b5-4dc3-8a55-08b0a23edaef status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task 71af4f14-0458-4f1e-9eb8-6530d1f407d1 status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task a5e395b7-847d-4add-8605-a62f9f1a8d76 status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task b769a8bc-dd12-44f6-8c30-0a3ed869877f status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task fd6f0630-5d4f-459a-86d0-32587119941c status updated to running
2025-05-28 17:27:56,128 - src.core.queue - INFO - Task 33f5c522-24ae-40dc-b0fe-5948cfc629a5 status updated to running
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task aebc5214-307a-4651-a832-685ad90cde8b status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task 91efe4b9-b9db-43e4-9fa8-ed981f0002c1 status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task 8e67677c-fb50-4c54-b90c-21f7037bd9e0 status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task 2029c0bf-b6c7-433b-96c9-c072653ce044 status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task 9e9da07b-86b5-4dc3-8a55-08b0a23edaef status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task 71af4f14-0458-4f1e-9eb8-6530d1f407d1 status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task a5e395b7-847d-4add-8605-a62f9f1a8d76 status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task b769a8bc-dd12-44f6-8c30-0a3ed869877f status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task fd6f0630-5d4f-459a-86d0-32587119941c status updated to completed
2025-05-28 17:27:56,139 - src.core.queue - INFO - Task 33f5c522-24ae-40dc-b0fe-5948cfc629a5 status updated to completed
