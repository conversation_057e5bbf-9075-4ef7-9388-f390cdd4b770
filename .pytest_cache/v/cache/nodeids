["test_api_and_queue.py::TestAPIModels::test_batch_task_request_validation", "test_api_and_queue.py::TestAPIModels::test_task_request_validation", "test_api_and_queue.py::TestAPIModels::test_task_response_model", "test_api_and_queue.py::TestAPIRoutes::test_create_batch_tasks_endpoint", "test_api_and_queue.py::TestAPIRoutes::test_create_task_endpoint", "test_api_and_queue.py::TestAPIRoutes::test_get_task_endpoint", "test_api_and_queue.py::TestAPIRoutes::test_get_task_not_found", "test_api_and_queue.py::TestAPIRoutes::test_list_tasks_endpoint", "test_api_and_queue.py::TestIntegration::test_concurrent_task_operations", "test_api_and_queue.py::TestIntegration::test_full_task_lifecycle", "test_api_and_queue.py::TestTaskQueue::test_dequeue_task", "test_api_and_queue.py::TestTaskQueue::test_enqueue_task", "test_api_and_queue.py::TestTaskQueue::test_list_tasks", "test_api_and_queue.py::TestTaskQueue::test_queue_size_limit", "test_api_and_queue.py::TestTaskQueue::test_update_task_status", "test_api_and_queue.py::TestWorker::test_worker_can_process_task", "test_api_and_queue.py::TestWorker::test_worker_initialization", "test_api_and_queue.py::TestWorker::test_worker_model_concurrency", "test_api_and_queue.py::TestWorker::test_worker_stats", "test_api_and_queue.py::TestWorkerPool::test_worker_pool_initialization", "test_api_and_queue.py::TestWorkerPool::test_worker_pool_stats"]