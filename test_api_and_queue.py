#!/usr/bin/env python3
"""
Unit tests for FastAPI and Task Queue functionality.

This module contains comprehensive tests for:
1. FastAPI routes and API models
2. Task queue operations
3. Worker functionality
4. Integration between API and queue systems
"""

import pytest
import pytest_asyncio
import asyncio
import json
import sys
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any, List
from fastapi.testclient import TestClient
from httpx import AsyncClient

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.api.routes import router
from src.api.models import (
    TaskRequest, BatchTaskRequest, TaskResponse, TaskResult,
    TaskStatus, BenchmarkInfo, WorkerStatsResponse
)
from src.core.queue import TaskQueue, Task, get_task_queue
from src.core.worker import Worker, WorkerPool, get_worker_pool
from src.core.config import get_config
from fastapi import FastAPI


# Test fixtures
@pytest.fixture
def app():
    """Create FastAPI test application."""
    app = FastAPI()
    app.include_router(router, prefix="/api")
    return app


@pytest.fixture
def client(app):
    """Create test client."""
    return TestClient(app)


@pytest_asyncio.fixture
async def async_client(app):
    """Create async test client."""
    from httpx import ASGITransport
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://test") as ac:
        yield ac


@pytest.fixture
def task_queue():
    """Create a fresh task queue for testing."""
    return TaskQueue(max_size=10)


@pytest.fixture
def mock_adapters():
    """Create mock adapters for testing."""
    mock_adapter = Mock()
    mock_adapter.evaluate = AsyncMock(return_value={"score": 0.85, "details": "test result"})
    mock_adapter.get_tasks = Mock(return_value=["task1", "task2"])
    mock_adapter.get_info = Mock(return_value={
        "name": "test_benchmark",
        "description": "Test benchmark",
        "tasks": [{"name": "task1"}, {"name": "task2"}]
    })

    return {
        "test_benchmark": mock_adapter,
        "tau_bench": mock_adapter,
        "bfc": mock_adapter,
        "gaia": mock_adapter
    }


@pytest.fixture
def worker(mock_adapters):
    """Create a test worker."""
    return Worker(
        worker_id="test-worker",
        adapters=mock_adapters,
        max_concurrent_tasks=2,
        timeout_seconds=30
    )


class TestTaskQueue:
    """Test cases for TaskQueue functionality."""

    @pytest.mark.asyncio
    async def test_enqueue_task(self, task_queue):
        """Test task enqueueing."""
        task_type = "test_benchmark"
        params = {"model": "test-model", "task": "test-task"}

        task_id = await task_queue.enqueue(task_type, params)

        assert task_id is not None
        assert len(task_id) > 0

        # Verify task was stored
        task = await task_queue.get_task(task_id)
        assert task is not None
        assert task.task_type == task_type
        assert task.params == params
        assert task.status == TaskStatus.PENDING

    @pytest.mark.asyncio
    async def test_dequeue_task(self, task_queue):
        """Test task dequeueing."""
        # Enqueue a task first
        task_id = await task_queue.enqueue("test_benchmark", {"model": "test-model"})

        # Dequeue the task
        dequeued_id = await task_queue.dequeue()

        assert dequeued_id == task_id

    @pytest.mark.asyncio
    async def test_update_task_status(self, task_queue):
        """Test task status updates."""
        task_id = await task_queue.enqueue("test_benchmark", {"model": "test-model"})

        # Update to running
        success = await task_queue.update_task_status(task_id, TaskStatus.RUNNING)
        assert success is True

        task = await task_queue.get_task(task_id)
        assert task.status == TaskStatus.RUNNING
        assert task.started_at is not None

        # Update to completed
        result = {"score": 0.9}
        success = await task_queue.update_task_status(
            task_id, TaskStatus.COMPLETED, result=result
        )
        assert success is True

        task = await task_queue.get_task(task_id)
        assert task.status == TaskStatus.COMPLETED
        assert task.result == result
        assert task.completed_at is not None

    @pytest.mark.asyncio
    async def test_list_tasks(self, task_queue):
        """Test listing tasks."""
        # Enqueue multiple tasks
        task_ids = []
        for i in range(3):
            task_id = await task_queue.enqueue(
                "test_benchmark",
                {"model": f"model-{i}"}
            )
            task_ids.append(task_id)

        # List all tasks
        tasks = await task_queue.list_tasks()
        assert len(tasks) == 3

        # List tasks by status
        pending_tasks = await task_queue.list_tasks(status=TaskStatus.PENDING)
        assert len(pending_tasks) == 3

        # Update one task and test filtering
        await task_queue.update_task_status(task_ids[0], TaskStatus.RUNNING)
        running_tasks = await task_queue.list_tasks(status=TaskStatus.RUNNING)
        assert len(running_tasks) == 1

        pending_tasks = await task_queue.list_tasks(status=TaskStatus.PENDING)
        assert len(pending_tasks) == 2

    @pytest.mark.asyncio
    async def test_queue_size_limit(self):
        """Test queue size limits."""
        small_queue = TaskQueue(max_size=2)

        # Fill the queue
        task_id1 = await small_queue.enqueue("test", {"model": "model1"})
        task_id2 = await small_queue.enqueue("test", {"model": "model2"})

        # Verify both tasks are in the queue
        assert task_id1 is not None
        assert task_id2 is not None

        # Verify we can retrieve the tasks
        task1 = await small_queue.get_task(task_id1)
        task2 = await small_queue.get_task(task_id2)
        assert task1 is not None
        assert task2 is not None


class TestWorker:
    """Test cases for Worker functionality."""

    @pytest.mark.asyncio
    async def test_worker_initialization(self, worker):
        """Test worker initialization."""
        assert worker.worker_id == "test-worker"
        assert worker.max_concurrent_tasks == 2
        assert worker.timeout_seconds == 30
        assert not worker.running
        assert len(worker.tasks) == 0

    @pytest.mark.asyncio
    async def test_worker_model_concurrency(self, worker):
        """Test worker model concurrency tracking."""
        # Test adding and removing tasks from active model tasks
        worker._add_to_active_model_tasks("task-1", "test-model")
        assert "task-1" in worker.active_model_tasks["test-model"]

        worker._remove_from_active_model_tasks("task-1")
        assert "task-1" not in worker.active_model_tasks["test-model"]

    @pytest.mark.asyncio
    async def test_worker_stats(self, worker):
        """Test worker statistics."""
        stats = worker.stats

        assert "tasks_completed" in stats
        assert "tasks_failed" in stats
        assert "tasks_cancelled" in stats
        assert "total_execution_time" in stats
        assert "model_stats" in stats

        assert stats["tasks_completed"] == 0
        assert stats["tasks_failed"] == 0


class TestWorkerPool:
    """Test cases for WorkerPool functionality."""

    @pytest.mark.asyncio
    async def test_worker_pool_initialization(self, mock_adapters):
        """Test worker pool initialization."""
        pool = WorkerPool(adapters=mock_adapters, worker_count=2)

        assert pool.worker_count == 2
        assert len(pool.workers) == 0
        assert not pool.running

    @pytest.mark.asyncio
    async def test_worker_pool_stats(self, mock_adapters):
        """Test worker pool statistics."""
        pool = WorkerPool(adapters=mock_adapters, worker_count=2)

        stats = await pool.get_stats()
        assert "tasks_completed" in stats
        assert "tasks_failed" in stats
        assert "tasks_cancelled" in stats
        assert "total_execution_time" in stats
        assert "model_stats" in stats

        assert stats["tasks_completed"] == 0
        assert stats["tasks_failed"] == 0


class TestAPIModels:
    """Test cases for API models validation."""

    def test_task_request_validation(self):
        """Test TaskRequest model validation."""
        # Valid request
        valid_request = TaskRequest(
            benchmark="tau_bench",
            model="gpt-4",
            task="test_task",
            framework="autogen",
            params={"temperature": 0.7}
        )
        assert valid_request.benchmark == "tau_bench"
        assert valid_request.model == "gpt-4"
        assert valid_request.task == "test_task"
        assert valid_request.framework == "autogen"
        assert valid_request.params["temperature"] == 0.7

        # Minimal valid request
        minimal_request = TaskRequest(
            benchmark="bfc",
            model="claude-3-sonnet"
        )
        assert minimal_request.benchmark == "bfc"
        assert minimal_request.model == "claude-3-sonnet"
        assert minimal_request.task is None
        assert minimal_request.framework is None
        assert minimal_request.params is None

    def test_batch_task_request_validation(self):
        """Test BatchTaskRequest model validation."""
        # Valid batch request
        valid_request = BatchTaskRequest(
            benchmark="gaia",
            models=["gpt-4", "claude-3-opus"],
            tasks=["task1", "task2"],
            framework="smolagents"
        )
        assert valid_request.benchmark == "gaia"
        assert len(valid_request.models) == 2
        assert len(valid_request.tasks) == 2

        # Test validation error for empty models
        with pytest.raises(ValueError):
            BatchTaskRequest(
                benchmark="tau_bench",
                models=[],  # Empty models should raise error
                tasks=["task1"]
            )

    def test_task_response_model(self):
        """Test TaskResponse model."""
        import time
        now = time.time()

        response = TaskResponse(
            task_id="test-task-123",
            benchmark="tau_bench",
            model="gpt-4",
            task="test_task",
            status=TaskStatus.RUNNING,
            created_at=now,
            started_at=now + 1,
            completed_at=None
        )

        assert response.task_id == "test-task-123"
        assert response.status == TaskStatus.RUNNING
        assert response.completed_at is None


class TestAPIRoutes:
    """Test cases for API routes."""

    @pytest.mark.asyncio
    async def test_create_task_endpoint(self, async_client):
        """Test POST /api/tasks endpoint."""
        with patch('src.api.routes.get_task_queue') as mock_get_queue:
            # Mock task queue
            mock_queue = AsyncMock()
            mock_task = Mock()
            mock_task.task_id = "test-task-123"
            mock_task.task_type = "tau_bench"
            mock_task.params = {"model": "gpt-4", "task": "test_task"}
            mock_task.status = TaskStatus.PENDING
            mock_task.created_at = 1234567890.0
            mock_task.started_at = None
            mock_task.completed_at = None

            mock_queue.enqueue.return_value = "test-task-123"
            mock_queue.get_task.return_value = mock_task
            mock_get_queue.return_value = mock_queue

            # Test request
            request_data = {
                "benchmark": "tau_bench",
                "model": "gpt-4",
                "task": "test_task"
            }

            response = await async_client.post("/api/tasks", json=request_data)

            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == "test-task-123"
            assert data["benchmark"] == "tau_bench"
            assert data["model"] == "gpt-4"
            assert data["status"] == "pending"

            # Verify queue methods were called
            mock_queue.enqueue.assert_called_once()
            mock_queue.get_task.assert_called_once_with("test-task-123")

    @pytest.mark.asyncio
    async def test_create_batch_tasks_endpoint(self, async_client):
        """Test POST /api/tasks/batch endpoint."""
        with patch('src.api.routes.get_task_queue') as mock_get_queue:
            # Mock task queue
            mock_queue = AsyncMock()
            mock_queue.enqueue.side_effect = ["task-1", "task-2"]

            # Mock tasks
            mock_task_1 = Mock()
            mock_task_1.task_id = "task-1"
            mock_task_1.task_type = "bfc"
            mock_task_1.params = {"model": "gpt-4"}
            mock_task_1.status = TaskStatus.PENDING
            mock_task_1.created_at = 1234567890.0
            mock_task_1.started_at = None
            mock_task_1.completed_at = None

            mock_task_2 = Mock()
            mock_task_2.task_id = "task-2"
            mock_task_2.task_type = "bfc"
            mock_task_2.params = {"model": "claude-3-sonnet"}
            mock_task_2.status = TaskStatus.PENDING
            mock_task_2.created_at = 1234567890.0
            mock_task_2.started_at = None
            mock_task_2.completed_at = None

            mock_queue.get_task.side_effect = [mock_task_1, mock_task_2]
            mock_get_queue.return_value = mock_queue

            # Test request
            request_data = {
                "benchmark": "bfc",
                "models": ["gpt-4", "claude-3-sonnet"]
            }

            response = await async_client.post("/api/tasks/batch", json=request_data)

            assert response.status_code == 200
            data = response.json()
            assert len(data) == 2
            assert data[0]["task_id"] == "task-1"
            assert data[1]["task_id"] == "task-2"

            # Verify queue methods were called
            assert mock_queue.enqueue.call_count == 2
            assert mock_queue.get_task.call_count == 2

    @pytest.mark.asyncio
    async def test_get_task_endpoint(self, async_client):
        """Test GET /api/tasks/{task_id} endpoint."""
        with patch('src.api.routes.get_task_queue') as mock_get_queue:
            # Mock task queue
            mock_queue = AsyncMock()
            mock_task = Mock()
            mock_task.task_id = "test-task-123"
            mock_task.task_type = "gaia"
            mock_task.params = {"model": "gpt-4", "framework": "autogen"}
            mock_task.status = TaskStatus.COMPLETED
            mock_task.result = {"score": 0.85}
            mock_task.error = None
            mock_task.created_at = 1234567890.0
            mock_task.started_at = 1234567891.0
            mock_task.completed_at = 1234567900.0

            mock_queue.get_task.return_value = mock_task
            mock_get_queue.return_value = mock_queue

            response = await async_client.get("/api/tasks/test-task-123")

            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == "test-task-123"
            assert data["benchmark"] == "gaia"
            assert data["status"] == "completed"
            assert data["result"]["score"] == 0.85

            mock_queue.get_task.assert_called_once_with("test-task-123")

    @pytest.mark.asyncio
    async def test_get_task_not_found(self, async_client):
        """Test GET /api/tasks/{task_id} with non-existent task."""
        with patch('src.api.routes.get_task_queue') as mock_get_queue:
            mock_queue = AsyncMock()
            mock_queue.get_task.return_value = None
            mock_get_queue.return_value = mock_queue

            response = await async_client.get("/api/tasks/non-existent")

            assert response.status_code == 404
            data = response.json()
            assert "not found" in data["detail"].lower()

    @pytest.mark.asyncio
    async def test_list_tasks_endpoint(self, async_client):
        """Test GET /api/tasks endpoint."""
        with patch('src.api.routes.get_task_queue') as mock_get_queue:
            # Mock task queue
            mock_queue = AsyncMock()
            mock_tasks = [
                {
                    "task_id": "task-1",
                    "task_type": "tau_bench",
                    "params": {"model": "gpt-4"},
                    "status": TaskStatus.COMPLETED,
                    "result": {"score": 0.9},
                    "error": None,
                    "created_at": 1234567890.0,
                    "started_at": 1234567891.0,
                    "completed_at": 1234567900.0
                },
                {
                    "task_id": "task-2",
                    "task_type": "bfc",
                    "params": {"model": "claude-3-sonnet"},
                    "status": TaskStatus.RUNNING,
                    "result": None,
                    "error": None,
                    "created_at": 1234567895.0,
                    "started_at": 1234567896.0,
                    "completed_at": None
                }
            ]

            mock_queue.list_tasks.return_value = mock_tasks
            mock_get_queue.return_value = mock_queue

            response = await async_client.get("/api/tasks")

            assert response.status_code == 200
            data = response.json()
            assert len(data) == 2
            assert data[0]["task_id"] == "task-1"
            assert data[0]["status"] == "completed"
            assert data[1]["task_id"] == "task-2"
            assert data[1]["status"] == "running"

            mock_queue.list_tasks.assert_called_once()


class TestIntegration:
    """Integration tests for API and Queue systems."""

    @pytest.mark.asyncio
    async def test_full_task_lifecycle(self, async_client):
        """Test complete task lifecycle from creation to completion."""
        with patch('src.api.routes.get_task_queue') as mock_get_queue:
            # Create a real task queue for this test
            real_queue = TaskQueue(max_size=10)
            mock_get_queue.return_value = real_queue

            # 1. Create a task
            request_data = {
                "benchmark": "tau_bench",
                "model": "gpt-4",
                "task": "test_task"
            }

            response = await async_client.post("/api/tasks", json=request_data)
            assert response.status_code == 200

            task_data = response.json()
            task_id = task_data["task_id"]
            assert task_data["status"] == "pending"

            # 2. Check task status
            response = await async_client.get(f"/api/tasks/{task_id}")
            assert response.status_code == 200

            task_data = response.json()
            assert task_data["status"] == "pending"

            # 3. Simulate task processing
            await real_queue.update_task_status(task_id, TaskStatus.RUNNING)

            response = await async_client.get(f"/api/tasks/{task_id}")
            task_data = response.json()
            assert task_data["status"] == "running"

            # 4. Complete the task
            result = {"score": 0.95, "details": "Test completed successfully"}
            await real_queue.update_task_status(
                task_id, TaskStatus.COMPLETED, result=result
            )

            response = await async_client.get(f"/api/tasks/{task_id}")
            task_data = response.json()
            assert task_data["status"] == "completed"
            assert task_data["result"]["score"] == 0.95

    @pytest.mark.asyncio
    async def test_concurrent_task_operations(self):
        """Test concurrent task operations on the queue."""
        queue = TaskQueue(max_size=50)

        # Create multiple tasks concurrently
        async def create_task(i):
            return await queue.enqueue(
                "test_benchmark",
                {"model": f"model-{i}", "task": f"task-{i}"}
            )

        # Create 10 tasks concurrently
        task_ids = await asyncio.gather(*[create_task(i) for i in range(10)])

        assert len(task_ids) == 10
        assert len(set(task_ids)) == 10  # All IDs should be unique

        # Verify all tasks are in the queue
        tasks = await queue.list_tasks()
        assert len(tasks) == 10

        # Process tasks concurrently
        async def process_task(task_id):
            await queue.update_task_status(task_id, TaskStatus.RUNNING)
            await asyncio.sleep(0.01)  # Simulate processing time
            await queue.update_task_status(
                task_id, TaskStatus.COMPLETED,
                result={"score": 0.8}
            )

        # Process all tasks
        await asyncio.gather(*[process_task(tid) for tid in task_ids])

        # Verify all tasks are completed
        completed_tasks = await queue.list_tasks(status=TaskStatus.COMPLETED)
        assert len(completed_tasks) == 10


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
